@{
    ViewData["Title"] = "Báo cáo thống kê sản phẩm đặt hàng theo khách hàng";
}

@section Styles {
    <link href="~/css/kendo-grid-common.css" rel="stylesheet" asp-append-version="true" />
    <link href="~/css/toolbar-common.css" rel="stylesheet" asp-append-version="true" />
}

<div>
    <h4 class="demo-section wide title">@ViewData["Title"]</h4>
    <div id="divContent">
        <div id="gridId">
        </div>
    </div>
    <div id="window"></div>
    <div id="dialog"></div>
</div>

<script type="text/javascript">
    let gridId = "#gridId";

    function getSearchModel() {
        let productName = $("#productName").val();
        let customerId = $("#customerId").data("kendoDropDownList")?.value();
        let dateFromPicker = $("#dateFrom").data("kendoDatePicker");
        let dateToPicker = $("#dateTo").data("kendoDatePicker");
        let includeDetails = $("#includeDetails").prop("checked");

        // Get date values from Kendo DatePickers
        let dateFrom = null;
        let dateTo = null;

        if (dateFromPicker && dateFromPicker.value()) {
            dateFrom = dateFromPicker.value().toISOString();
        }

        if (dateToPicker && dateToPicker.value()) {
            let dateToObj = dateToPicker.value();
            // Set to end of day
            dateToObj.setHours(23, 59, 59, 999);
            dateTo = dateToObj.toISOString();
        }

        return {
            productName: productName || null,
            customer_ID: customerId && customerId !== "" ? parseInt(customerId) : null,
            dateFrom: dateFrom,
            dateTo: dateTo,
            includeDetails: includeDetails
        };
    }

    async function exportToExcel() {
        // Create header for Excel export
        let dataSheet1 = [
            {
                cells: [
                    { value: "Khách hàng", textAlign: "center", background: "#428dd8" },
                    { value: "Mã sản phẩm", textAlign: "center", background: "#428dd8" },
                    { value: "Tên sản phẩm", textAlign: "center", background: "#428dd8" },
                    { value: "Đơn vị", textAlign: "center", background: "#428dd8" },
                    { value: "Tổng số lượng", textAlign: "center", background: "#428dd8" },
                    { value: "Giá thấp nhất", textAlign: "center", background: "#428dd8" },
                    { value: "Giá cao nhất", textAlign: "center", background: "#428dd8" },
                    { value: "Giá trung bình", textAlign: "center", background: "#428dd8" },
                    { value: "Giá hiện tại", textAlign: "center", background: "#428dd8" },
                    { value: "Tổng giá trị", textAlign: "center", background: "#428dd8" },
                    { value: "Tổng tiền đặt hàng", textAlign: "center", background: "#428dd8" },
                    { value: "Số PO", textAlign: "center", background: "#428dd8" }
                ]
            }];

        var searchModel = getSearchModel();
        let postData = {
            ...searchModel,
            pageSize: 999999999,
            pageNumber: 1
        }
        let dataSourceStatistics = null;
        var response = await ajax("GET", "/ProductOrderStatistics/GetCustomerProductOrderStatistics", postData, (response) => {
            dataSourceStatistics = response.data.data;
        }, null, false);
        if (dataSourceStatistics == null) return;

        // Process data for Excel export
        for (let customerStat of dataSourceStatistics) {
            for (let product of customerStat.products) {
                dataSheet1.push({
                    cells: [
                        { value: customerStat.customer.name },
                        { value: product.productCode },
                        { value: product.productName },
                        { value: product.unitName || "" },
                        { value: product.totalQuantity, format: "#,##0.00" },
                        { value: product.minPrice || 0, format: "#,##0" },
                        { value: product.maxPrice || 0, format: "#,##0" },
                        { value: product.averagePrice || 0, format: "#,##0" },
                        { value: product.currentPrice || 0, format: "#,##0" },
                        { value: product.totalValue, format: "#,##0" },
                        { value: product.totalOrderAmount, format: "#,##0" },
                        { value: product.poCount }
                    ]
                });
            }
        }

        var workbook = new kendo.ooxml.Workbook({
            sheets: [
                {
                    name: "Báo cáo thống kê khách hàng",
                    columns: [
                        { autoWidth: true }, { autoWidth: true }, { autoWidth: true },
                        { autoWidth: true }, { autoWidth: true }, { autoWidth: true },
                        { autoWidth: true }, { autoWidth: true }, { autoWidth: true },
                        { autoWidth: true }, { autoWidth: true }, { autoWidth: true }
                    ],
                    rows: dataSheet1
                }
            ]
        });
        kendo.saveAs({
            dataURI: workbook.toDataURL(),
            fileName: "Báo cáo thống kê khách hàng _ " + kendo.toString(new Date(), "dd_MM_yyyy__HH_mm_ss") + ".xlsx"
        });
    }

    $(document).ready(function () {
        // Initialize toolbar
        let toolbar = `
                <div id='toolbar' style=''  class='w-100 d-flex flex-column'>
                       <div class="row gx-0 row-gap-2 w-100">
                            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12">
                                <div class="pe-1">
                                    <label for="productName">Tên sản phẩm:</label>
                                    <input type="text" class="w-100" id="productName" placeholder="Nhập tên sản phẩm..."/>
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12">
                                <div class="pe-1">
                                    <label for="customerId">Khách hàng:</label>
                                    <select id="customerId" class="w-100"></select>
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12">
                                <div class="pe-1">
                                    <label for="dateFrom">Từ ngày:</label>
                                    <input type="text" class="w-100" id="dateFrom"/>
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12">
                                <div class="pe-1">
                                    <label for="dateTo">Đến ngày:</label>
                                    <input type="text" class="w-100" id="dateTo"/>
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12">
                                <div class="pe-1">
                                    <label for="includeDetails">Chi tiết PO:</label>
                                    <input type="checkbox" id="includeDetails"/>
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12 d-flex align-items-end">
                                <div class="pe-1 d-flex gap-2">
                                    <button id="search" title="Tìm kiếm" class="k-button k-button-md k-rounded-md k-button-solid k-button-solid-primary k-icon-button"><span class='k-icon k-i-search k-button-icon'></span><span class='k-button-text d-none'>Tìm kiếm</span></button>
                                    <button id="exportExcel" class="k-button k-button-md k-rounded-md k-button-outline k-button-outline-error"><span class="k-icon k-i-file-excel k-button-icon"></span><span class="k-button-text">Export Excel</span></button>
                                </div>
                            </div>
                        </div>
                </div>
            `;



        // Initialize main grid
        $(gridId).kendoGrid({
            toolbar: toolbar,
            dataSource: {
                transport: {
                    read: {
                        url: "/ProductOrderStatistics/GetCustomerProductOrderStatistics",
                        datatype: "json",
                    },
                    parameterMap: function (data, type) {
                        if (type == "read") {
                            var searchModel = getSearchModel();
                            return {
                                ...searchModel,
                                pageSize: data.pageSize,
                                pageNumber: data.page
                            }
                        }
                    },
                },
                serverPaging: true,
                serverFiltering: true,
                page: 1,
                pageSize: 20,
                schema: {
                    type: 'json',
                    parse: function (response) {
                        if (response.isSuccess == false) {
                            showErrorMessages(response.errorMessageList);
                            return {
                                data: [],
                                total: 0
                            }
                        }
                        return response.data;
                    },
                    model: {
                        id: "customer.id",
                        fields: {
                            totalValue: { type: "number" },
                            totalOrderAmount: { type: "number" },
                            confirmedPOCount: { type: "number" }
                        }
                    },
                    data: "data",
                    total: "total"
                },
            },
            selectable: true,
            pageable: {
                pageSizes: [10, 20, 50],
            },
            dataBinding: function (e) {
                showLoading();
            },
            dataBound: function (e) {
                hideLoading();
            },
            scrollable: true,
            sortable: true,
            columns: [
                {
                    field: "customer.code",
                    title: "Mã khách hàng",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:left;" },
                    width: 120,
                },
                {
                    field: "customer.name",
                    title: "Tên khách hàng",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:left;" },
                    width: 200,
                },
                {
                    field: "confirmedPOCount",
                    title: "Số PO đã xác nhận",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    width: 120,
                },
                {
                    field: "totalValue",
                    title: "Tổng giá trị",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:right;" },
                    width: 150,
                    template: "#= '<span class=\"currency\">' + kendo.toString(totalValue, 'n0') + ' VNĐ</span>' #"
                },
                {
                    field: "totalOrderAmount",
                    title: "Tổng tiền đặt hàng",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:right;" },
                    width: 150,
                    template: "#= '<span class=\"currency\">' + kendo.toString(totalOrderAmount, 'n0') + ' VNĐ</span>' #"
                }
            ],
            detailInit: detailInit,
            detailExpand: function (e) {
                this.collapseRow(this.tbody.find(' > tr.k-detail-row').not(e.detailRow));
            }
        });

        // Initialize Kendo components
        $("#search").kendoButton({
            icon: "search"
        });
        $("#search").click(function () {
            $(gridId).data("kendoGrid").dataSource.page(1);
        });

        $("#exportExcel").click(function () {
            exportToExcel();
        });

        $("#productName").kendoTextBox({
            icon: {
                type: "search",
                position: "end"
            },
            placeholder: "Nhập tên sản phẩm..."
        });


        // Initialize customer dropdown
        $("#customerId").kendoDropDownList({
            dataTextField: "text",
            dataValueField: "value",
            optionLabel: "-- Chọn khách hàng --",
            template: function (dataItem) {
                if (!dataItem || !dataItem.text) {
                    return "N/A";
                }
                return dataItem.text;
            },
            valueTemplate: function (dataItem) {
                if (!dataItem || !dataItem.text) {
                    return "-- Chọn khách hàng --";
                }
                return dataItem.text;
            },
            dataSource: {
                transport: {
                    read: {
                        url: "/Common/GetDataOptionsDropdown",
                        data: { type: "Customer" }
                    }
                },
                schema: {
                    parse: function (response) {
                        if (response && response.isSuccess && response.data) {
                            return response.data.filter(function (item) {
                                return item && item.text && item.value !== undefined;
                            });
                        }
                        return [];
                    }
                }
            }
        });

        // Set default date range to last 1 month
        var today = EndDay();
        var oneMonthAgo = new Date();
        oneMonthAgo.setMonth(today.getMonth() - 1);

        // Initialize Date Pickers
        $("#dateFrom").kendoDatePicker({
            format: "dd/MM/yyyy",
            culture: "vi-VN",
            value: oneMonthAgo
        });

        $("#dateTo").kendoDatePicker({
            format: "dd/MM/yyyy",
            culture: "vi-VN",
            value: today
        });

        // Trigger resize to set fixed grid height like PurchaseOrderIndex
        $(window).trigger("resize");

        // Auto load data with default filter (last 1 month)
        // setTimeout(function () {
        //     var grid = $(gridId).data("kendoGrid");
        //     if (grid) {
        //         grid.dataSource.read();
        //     }
        // }, 500);
    });

    function detailInit(e) {
        $("<div/>").appendTo(e.detailCell).kendoGrid({
            dataSource: {
                data: e.data.products,
                pageSize: 10
            },
            scrollable: false,
            sortable: true,
            pageable: false,
            columns: [
                { field: "productCode", title: "Mã sản phẩm", width: "120px" },
                { field: "productName", title: "Tên sản phẩm", width: "200px" },
                { field: "unitName", title: "Đơn vị", width: "100px" },
                { field: "totalQuantity", title: "Tổng SL", width: "100px", template: "#= '<span class=\"number\">' + kendo.toString(totalQuantity, 'n2') + '</span>' #" },
                { field: "minPrice", title: "Giá thấp nhất", width: "120px", template: "#= minPrice ? '<span class=\"currency\">' + kendo.toString(minPrice, 'n0') + ' VNĐ</span>' : '' #" },
                { field: "maxPrice", title: "Giá cao nhất", width: "120px", template: "#= maxPrice ? '<span class=\"currency\">' + kendo.toString(maxPrice, 'n0') + ' VNĐ</span>' : '' #" },
                { field: "averagePrice", title: "Giá TB", width: "120px", template: "#= averagePrice ? '<span class=\"currency\">' + kendo.toString(averagePrice, 'n0') + ' VNĐ</span>' : '' #" },
                { field: "currentPrice", title: "Giá hiện tại", width: "120px", template: "#= currentPrice ? '<span class=\"currency\">' + kendo.toString(currentPrice, 'n0') + ' VNĐ</span>' : '' #" },
                { field: "totalValue", title: "Tổng giá trị", width: "150px", template: "#= '<span class=\"currency\">' + kendo.toString(totalValue, 'n0') + ' VNĐ</span>' #" },
                { field: "totalOrderAmount", title: "Tổng tiền đặt hàng", width: "150px", template: "#= '<span class=\"currency\">' + kendo.toString(totalOrderAmount, 'n0') + ' VNĐ</span>' #" },
                { field: "poCount", title: "Số PO", width: "80px", template: "#= '<span class=\"number\">' + poCount + '</span>' #" }
            ]
        });
    }
</script>

<style>
    .k-form-buttons {
        justify-content: flex-end;
    }
</style>
